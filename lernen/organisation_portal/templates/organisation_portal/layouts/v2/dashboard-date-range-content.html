{% load json %}
{% load currency_utils %}
{% load displaytime %}
{% load displayPercentage %}
{% load round_to_decimals %}
{% load math_utils %}
{% load string_utils %}
<p style="display:none;" id="view-fees-stats-flag">{{view_fees_stats|yesno:"true,false"}}</p>
<style>
.stats-widget-loader {
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 10;
}

/*
=== VIBRANT LIGHT CHART COLORS ===
Theme Base: #43a2ad

=== STUDENT DISTRIBUTION CHART COLORS ===
Light vibrant variations of the theme for pie charts
*/
:root {
    /* Student Distribution - Light vibrant theme variations */
    --student-chart-color-1: #7bc4ce;  /* Light vibrant primary */
    --student-chart-color-2: #9dd4dc;  /* Lighter vibrant */
    --student-chart-color-3: #bfe4ea;  /* Very light vibrant */
    --student-chart-color-4: #d4eef2;  /* Pale vibrant */
    --student-chart-color-5: #e8f7f9;  /* Ultra light vibrant */
    --student-chart-color-6: #f2fbfc;  /* Whisper light */

    /* Fee Payment Distribution - Light warm variations */
    --fee-payment-color-1: #6bb8c4;   /* Light teal-blue */
    --fee-payment-color-2: #8cc8d2;   /* Soft aqua */
    --fee-payment-color-3: #add8e0;   /* Light aqua */
    --fee-payment-color-4: #cee8ee;   /* Pale aqua */
    --fee-payment-color-5: #e6f4f7;   /* Very light aqua */
    --fee-payment-color-6: #f0f9fb;   /* Whisper aqua */

    /* Gender Distribution - Light complementary colors */
    --gender-male-color: #5cb3bf;     /* Light vibrant male */
    --gender-female-color: #f4a6cd;   /* Light vibrant pink */

    /* Fee Head Distribution - Dynamic light palette */
    --fee-head-color-1: #7bc4ce;      /* Primary light */
    --fee-head-color-2: #a8d5a8;      /* Light green */
    --fee-head-color-3: #f4c2a1;      /* Light orange */
    --fee-head-color-4: #d4a5d4;      /* Light purple */
    --fee-head-color-5: #f4d03f;      /* Light yellow */
    --fee-head-color-6: #85c1e9;      /* Light blue */
    --fee-head-color-7: #f1948a;      /* Light coral */
    --fee-head-color-8: #aed6f1;      /* Light sky */
    --fee-head-color-9: #d5dbdb;      /* Light gray */
    --fee-head-color-10: #f8c471;     /* Light amber */
}
</style>
<p style="display:none;" id="home-page-stats">{{institute_details_map|jsonstr}}</p>
<div class="row">
    <div class="col-12 col-sm-6 col-xxl d-flex">
      <div class="card flex-fill">
        <div class="card-body py-4">
          <div class="media">
            <div class="media-body">
              <div class="student-count-stats">
                {% include 'core/utils/widget_loader.html' %}
                <h3 class="mb-2 stats-widget-content"></h3>
                <p class="mb-2">Enrolled Students</p>
              </div>
              <div class="student-admission-stats">
                  <!-- {% include 'core/utils/widget_loader.html' with width="1rem" height="1rem" %} -->
                  <div class="mb-0 mt-3 stats-widget-content" style="display: none;" >
                  <span class="badge badge-soft-success" >
                      <i class="mdi mdi-arrow-bottom-right"></i>
                      <span class="stats-widget-content-admission"></span>
                    </span>
                    <span class="text-muted">Admissions</span>
                    &nbsp;
                    <span class="badge badge-soft-danger" >
                      <i class="mdi mdi-arrow-bottom-right"></i>
                      <span class="stats-widget-content-tc"></span>
                    </span>
                    <span class="text-muted">TC Issued</span>
                  </div>
              </div>
          </div>
          <div class="d-inline-block ml-3">
              <div class="stat">
                <i class="align-middle text-success" data-feather="users"></i>
              </div>
          </div>
        </div>
      </div>
    </div>
    </div>
    <div class="col-12 col-sm-6 col-xxl d-flex">
      <div class="card flex-fill">
        <div class="card-body py-4">
          <div class="media">
            <div class="media-body">
              <div class="staff-count-stats">
                  {% include 'core/utils/widget_loader.html' %}
                  <h3 class="mb-2 stats-widget-content"></h3>
                  <p class="mb-2">Total Staff</p>
              </div>
              <div class="staff-attendance-stats">
                  <!-- {% include 'core/utils/widget_loader.html' with width="1rem" height="1rem" %} -->
                  <div class="mb-0 mt-3 stats-widget-content" style="display: none;" >
                    <span class="badge badge-soft-success" >
                      <i class="mdi mdi-arrow-bottom-right"></i>
                      <span class="stats-widget-content-staff-present"></span>
                    </span>
                    <span class="text-muted">Present</span>
                    &nbsp;
                    <span class="badge badge-soft-danger" >
                      <i class="mdi mdi-arrow-bottom-right"></i>
                      <span class="stats-widget-content-staff-leave"></span>
                    </span>
                    <span class="text-muted">Leave</span>
                  </div>
              </div>
             
            </div>
            <div class="d-inline-block ml-3">
              <div class="stat">
                <i class="align-middle text-success" data-feather="users"></i>
              </div>
          </div>
          </div>
        </div>
      </div>
    </div>
    <div class="col-12 col-sm-6 col-xxl d-flex student-attendance-stats">
        <div class="card flex-fill">
          {% include 'core/utils/widget_loader.html' %}
          <div class="stats-widget-content"></div>
        </div>
    </div>
    {% if view_fees_stats %}
    <div class="col-12 col-sm-6 col-xxl d-flex fee-collection-stats">
      <div class="card flex-fill">
        <div class="card-body py-4">
          <div class="media">
            <div class="media-body">

              <div>
                  {% include 'core/utils/widget_loader.html' %}
                  <div>
                    <h3 class="mb-2"><i class="align-middle me-2 fas fa-fw fa-rupee-sign"></i>
                      <span class="mb-2 stat-value"></span>
                    </h3>
                    <p class="mb-2">Total Fees Collection</p>
                  </div>
              </div>
              <!-- <div class="staff-attendance-stats">
                  {% include 'core/utils/widget_loader.html' with width="1rem" height="1rem" %}
                  <div class="mb-0 mt-3 stats-widget-content" style="display: block;" >
                    <span class="badge badge-soft-success" >
                      <i class="mdi mdi-arrow-bottom-right"></i>
                      <span class="stats-widget-content-staff-present"></span>
                    </span>
                    <span class="text-muted">Present</span>
                    &nbsp;
                    <span class="badge badge-soft-danger" >
                      <i class="mdi mdi-arrow-bottom-right"></i>
                      <span class="stats-widget-content-staff-leave"></span>
                    </span>
                    <span class="text-muted">Leave</span>
                  </div>
              </div> -->

            </div>
            <div class="d-inline-block ml-3">
              <div class="stat">
                <span class="iconify" data-icon="bx-bx-rupee" data-inline="false"></span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    {% endif %}
</div>
<div class="row" >
    <div class="col-6 col-lg-3 d-flex student-count-distribution-stats">
        <div class="card flex-fill w-100" style="height: 450px;">
            <div class="card-header">
              <h5 class="card-title mb-0">Student Distribution</h5>
            </div>
            <div style="overflow-y: auto; flex: 1; position: relative;">
                {% include 'core/utils/widget_loader.html' %}
                <div class="align-self-center w-100 stats-widget-content" style="display: none;">
                    <div class="py-3">
                        <div class="chart chart-xs">
                            <canvas id="chartjs-student-count-pie"></canvas>
                        </div>
                    </div>
                    <div class="student-institute-count-table">

                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-6 col-lg-5 d-flex staff-gender-distribution-stats">
        <div class="card flex-fill w-100" style="height: 450px;">
            <div class="card-header">
              <h5 class="card-title mb-0">Staff Gender Distribution</h5>
            </div>
            <br/>
            <div style="overflow-y: auto; flex: 1; position: relative;">
                {% include 'core/utils/widget_loader.html' %}
                <div class="align-self-center chart chart-lg stats-widget-content" style="display: none;">
                    <canvas id="chartjs-staff-gender-distribution"></canvas>
                </div>
            </div>
        </div>
    </div>
    {% if view_fees_stats %}
    <div class="col-6 col-lg-4 d-flex fee-collection-payment-mode-distribution-stats">
        <div class="card flex-fill w-100" style="height: 450px;">
            <div class="card-header">
              <h5 class="card-title mb-0">Fee Collection By Payment Mode</h5>
            </div>
            <div style="overflow-y: auto; flex: 1; position: relative;">
                {% include 'core/utils/widget_loader.html' %}
                <div class="align-self-center w-100 stats-widget-content" style="display: none;">
                    <div class="py-3">
                        <div class="chart chart-xs">
                            <canvas id="chartjs-fee-collection-payment-mode-pie"></canvas>
                        </div>
                    </div>
                    <div class="fee-collection-institute-table">

                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>
<div class="row">
    {% include 'organisation_portal/layouts/v2/birthday_widget.html' %}
    <div class="col-12 col-lg-3 d-flex active-transport-stats">
        <div class="card flex-fill w-100" style="height: 450px;">
            <div class="card-header">
              <h5 class="card-title mb-0">Active Transport Students</h5>
            </div>
            <div style="overflow-y: auto; flex: 1; position: relative;">
                {% include 'core/utils/widget_loader.html' %}
                <div class="align-self-center w-100 stats-widget-content" style="display: none;">
                    <div class="py-3">
                        <div class="chart chart-xs">
                            <canvas id="chartjs-active-student-transport-pie"></canvas>
                        </div>
                    </div>
                    <div class="active-student-transport-institute-table">

                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- <div class="col-12 col-lg-3 d-flex">
      <div class="card flex-fill w-100">
          <div class="card-header">
            <h5 class="card-title mb-0">Active Transport Students</h5>
          </div>
          <div class="card-body d-flex" style="position: relative;">
              {% include 'core/utils/widget_loader.html' %}
              <div class="align-self-center w-100 stats-widget-content" style="display: none;">
                  <div class="py-3">
                      <div class="chart chart-xs">
                        <canvas id="chartjs-active-transport-pie"></canvas>
                      </div>
                  </div>
                  <table class="table mb-0">
                      <thead>
                          <tr>
                              <th>Branches</th>
                              <th class="text-center">Transport/Total</th>
                          </tr>
                      </thead>
                      <tbody>
                          {% for institute_unique_key, institute_stats in institute_details_map.items %}
                          <tr>
                              <td><i class="fas fa-square-full" style='color: {{institute_stats.color}}'></i>&nbsp;&nbsp;{% if institute_stats.institute.branchName %}{{institute_stats.institute.branchName}}{% else %}{{institute_stats.institute.instituteName}}{% endif %}</td>
                              <td class="text-center">{% if institute_stats.transport_stats %}{{institute_stats.transport_stats.totalTransportAssignedStudentCount}}/{{institute_stats.transport_stats.totalEnrolledStudentCount}}{% else %}-{% endif %}</td>
                          </tr>
                          {% endfor %}
                      </tbody>
                  </table>
              </div>
          </div>
      </div>
    </div> -->
    {% if view_fees_stats %}
    <div class="col-12 col-lg-6 d-flex fee-collection-fee-head-distribution-stats">
        <div class="card flex-fill w-100">
            <div class="card-header">
              <h5 class="card-title mb-0">Fee Collection Distribution</h5>
            </div>
            <div style="position: relative;">
                {% include 'core/utils/widget_loader.html' %}
                <div class="align-self-center chart chart-lg stats-widget-content" style="display: none;">
                    <canvas id="chartjs-fee-collection-fee-head-distribution"></canvas>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>